from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from polar_sdk import Polar
from polar_sdk.webhooks import validate_event, WebhookVerificationError
from supabase import create_client, Client
import os
import uvicorn
import dotenv
import json

app = FastAPI(title="Handwrite API", description="Polar SDK Backend", version="1.0.0")
dotenv.load_dotenv()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Configuration
POLAR_ACCESS_TOKEN = os.getenv('POLAR_ACCESS_TOKEN', '<YOUR_BEARER_TOKEN_HERE>')
PRODUCT_PLAN_ID = os.getenv('PRODUCT_PLAN_ID', '<YOUR_PRODUCT_PLAN_ID>')
POLAR_WEBHOOK_SECRET = os.getenv('POLAR_WEBHOOK_SECRET', '<YOUR_WEBHOOK_SECRET>')

# Supabase Configuration
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_SERVICE_ROLE_KEY = os.getenv('SUPABASE_SERVICE_ROLE_KEY')

# Initialize Supabase client
supabase: Client = None
if SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY:
    supabase = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

# Pydantic models
class CustomerBillingAddress(BaseModel):
    country: str

class CheckoutRequest(BaseModel):
    customer_billing_address: CustomerBillingAddress
    product_plan_id: Optional[str] = None
    embed_origin: Optional[str] = None
    user_id: Optional[str] = None

class MultipleCheckoutRequest(BaseModel):
    customer_billing_address: CustomerBillingAddress
    product_plan_ids: List[str]

class HealthResponse(BaseModel):
    status: str
    message: str

class CheckoutResponse(BaseModel):
    success: bool
    checkout: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class WebhookResponse(BaseModel):
    success: bool
    message: str

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    return HealthResponse(status="healthy", message="Handwrite API is running")

@app.post("/create-checkout", response_model=CheckoutResponse)
async def create_checkout(request: CheckoutRequest):
    """
    Create a checkout session with Polar
    
    Request body:
    - customer_billing_address: CustomerBillingAddress object with country
    - product_plan_id: Optional product plan ID override
    """
    try:
        # Use provided product plan ID or default
        product_plan_id = request.product_plan_id or PRODUCT_PLAN_ID
        
        if product_plan_id == '<YOUR_PRODUCT_PLAN_ID>':
            raise HTTPException(status_code=500, detail="Product plan ID not configured")
        
        # Initialize Polar client
        with Polar(access_token=POLAR_ACCESS_TOKEN, server="sandbox") as polar:
            
            # Create checkout request
            checkout_request = {
                "customer_billing_address": request.customer_billing_address.dict(),
                "products": [product_plan_id]
            }

            # Add embed_origin if provided
            if request.embed_origin:
                checkout_request["embed_origin"] = request.embed_origin

            # Add external_customer_id if user_id is provided
            if request.user_id:
                checkout_request["external_customer_id"] = request.user_id
            
            # Create checkout with Polar
            res = polar.checkouts.create(request=checkout_request)
            
            # Return the response
            return CheckoutResponse(
                success=True,
                checkout=res.model_dump() if hasattr(res, 'model_dump') else res
            )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/create-checkout-multiple", response_model=CheckoutResponse)
async def create_checkout_multiple(request: MultipleCheckoutRequest):
    """
    Create a checkout session with multiple products
    
    Request body:
    - customer_billing_address: CustomerBillingAddress object with country
    - product_plan_ids: List of product plan IDs
    """
    try:
        if not request.product_plan_ids:
            raise HTTPException(status_code=400, detail="product_plan_ids array is required")
        
        # Initialize Polar client
        with Polar(access_token=POLAR_ACCESS_TOKEN) as polar:
            
            # Create checkout request
            checkout_request = {
                "customer_billing_address": request.customer_billing_address.dict(),
                "products": request.product_plan_ids
            }
            
            # Create checkout with Polar
            res = polar.checkouts.create(request=checkout_request)
            
            # Return the response
            return CheckoutResponse(
                success=True,
                checkout=res.model_dump() if hasattr(res, 'model_dump') else res
            )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/webhook/polar", response_model=WebhookResponse)
async def polar_webhook(request: Request):
    """
    Handle Polar webhook events, specifically subscription.updated
    """
    try:
        event = validate_event(
            body=await request.body(),
            headers=dict(request.headers),
            secret=POLAR_WEBHOOK_SECRET,
        )

        # Get the raw body
        body = await request.body()
        payload = json.loads(body)

        # Check if this is a subscription.updated event
        if payload.get("type") != "subscription.updated":
            return WebhookResponse(success=True, message="Event type not handled")

        # Extract subscription data
        subscription_data = payload.get("data", {})
        external_customer_id = subscription_data.get("customer").get("external_id")
        plan_status = subscription_data.get("status")

        if not external_customer_id:
            return WebhookResponse(success=True, message="No external_customer_id found")

        if not supabase:
            raise HTTPException(status_code=500, detail="Supabase not configured")

        plan_type = "pro"
        plan_expires_at = subscription_data.get("current_period_end")

        # Update user profile in Supabase
        update_data = {
            "plan_status": plan_status,
            "plan_type": plan_type if plan_status == "active" else "free",
            "plan_expires_at": plan_expires_at if plan_status == "active" else None,
        }

        result = supabase.table("profiles").update(update_data).eq("user_id", external_customer_id).execute()

        if result.data:
            return WebhookResponse(success=True, message="User profile updated successfully")
        else:
            return WebhookResponse(success=True, message="User not found or no changes made")

    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON payload")
    except WebhookVerificationError:
        raise HTTPException(status_code=403, detail="Webhook verification failed")
    except Exception as e:
        print(f"Webhook error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == '__main__':
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
