# Supabase Integration Implementation

This document outlines the Supabase integration implemented for the scribble-to-pdf-craft application, enabling persistent storage of user editor state, custom backgrounds, and custom fonts.

## Overview

The integration replaces localStorage-based storage with Supabase for:
- **Editor state persistence** - Text, configurations, and settings stored as JSON
- **Custom background images** - Uploaded to Supabase Storage
- **Custom font files** - Uploaded to Supabase Storage with proper validation

## Database Schema

### `user_editor_state` Table
```sql
CREATE TABLE user_editor_state (
  user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  editor_state JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Editor State JSON Structure:**
```json
{
  "text": "User's handwritten text content",
  "selectedFont": "handwriting",
  "background": "lined",
  "customBackground": "https://storage-url/user-backgrounds/user123/bg1.jpg",
  "typographyConfig": {
    "fontSize": 18,
    "lineHeight": 1.6,
    "textColor": "#000000",
    "handwritingEffects": { ... }
  },
  "textPosition": { "x": 0, "y": 0 },
  "showPageNumbers": false,
  "customFonts": [
    {
      "id": "font-uuid",
      "name": "My Custom Font",
      "family": "CustomFont123",
      "url": "https://storage-url/user-fonts/user123/font1.woff2",
      "format": "woff2",
      "originalFileName": "MyFont.woff2",
      "storagePath": "user123/font1.woff2"
    }
  ]
}
```

## Storage Buckets

### `user-backgrounds` Bucket
- **Purpose**: Store custom background images
- **File size limit**: 5MB
- **Allowed types**: JPEG, PNG, WebP, GIF
- **Structure**: `{user_id}/{timestamp}-{random}.{ext}`

### `user-fonts` Bucket
- **Purpose**: Store custom font files
- **File size limit**: 2MB
- **Allowed types**: WOFF2, WOFF, TTF, OTF
- **Structure**: `{user_id}/{timestamp}-{random}.{ext}`

## Row Level Security (RLS)

All tables and storage buckets have RLS policies ensuring users can only access their own data:

```sql
-- Table policies
CREATE POLICY "Users can view their own editor state" ON user_editor_state
  FOR SELECT USING (auth.uid() = user_id);

-- Storage policies
CREATE POLICY "Users can upload their own files" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'user-backgrounds' AND 
    auth.uid()::text = (storage.foldername(name))[1]
  );
```

## Implementation Files

### Core Services
- **`src/lib/storage.ts`** - Storage utilities for file uploads
- **`src/lib/editorState.ts`** - Editor state management with Supabase sync
- **`src/utils/fontManager.ts`** - Enhanced font management with Supabase storage

### Updated Components
- **`src/components/HandwritingEditor.tsx`** - Main editor with Supabase integration
- **`src/components/ui/background-gallery.tsx`** - Background upload to Supabase
- **`src/components/ui/enhanced-toolbar.tsx`** - Updated interface for new handlers

## Key Features

### 1. **Automatic State Sync**
- Debounced saves (1 second delay) to prevent excessive API calls
- Automatic loading on component mount
- localStorage fallback for offline scenarios

### 2. **File Upload Management**
- Client-side validation before upload
- Unique filename generation to prevent conflicts
- Automatic cleanup of old blob URLs
- Progress indication and error handling

### 3. **Cross-Device Continuity**
- Editor state syncs across all user devices
- Custom fonts and backgrounds available everywhere
- Real-time updates with debounced saving

### 4. **Offline Support**
- localStorage used as backup during saves
- Graceful fallback when Supabase is unavailable
- Automatic sync when connection is restored

## Usage Examples

### Loading Editor State
```typescript
import { loadEditorState } from '@/lib/editorState';

const result = await loadEditorState(userId);
if (result.success && result.data) {
  // Apply loaded state to components
  setText(result.data.text);
  setBackground(result.data.background);
}
```

### Uploading Custom Background
```typescript
import { uploadBackgroundImage } from '@/lib/storage';

const result = await uploadBackgroundImage(file, userId);
if (result.success) {
  setCustomBackground(result.url);
}
```

### Uploading Custom Font
```typescript
import { uploadCustomFont } from '@/utils/fontManager';

const customFont = await uploadCustomFont(file, userId);
// Font is automatically uploaded to storage and CSS is generated
```

## Testing

A test file is available at `src/test-supabase-integration.ts` to verify:
- Database table access
- Storage bucket permissions
- Editor state save/load functionality

Run tests in browser console:
```javascript
// Access test functions
window.testSupabase.runAllTests();
```

## Migration Strategy

The implementation maintains backward compatibility:
1. **Gradual migration**: localStorage is still used as backup
2. **Fallback support**: Works offline with localStorage
3. **No data loss**: Existing localStorage data is preserved and migrated

## Security Considerations

- **RLS policies** ensure data isolation between users
- **File validation** prevents malicious uploads
- **Size limits** prevent storage abuse
- **MIME type checking** ensures only valid files are accepted
- **Authentication required** for all storage operations

## Performance Optimizations

- **Debounced saves** reduce API calls
- **Lazy loading** of editor state
- **Efficient JSON storage** in single field
- **CDN delivery** for uploaded assets
- **Memory cleanup** for blob URLs
