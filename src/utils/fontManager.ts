/**
 * Font Manager Utility
 * Handles custom font uploads, validation, and CSS injection
 */

import { uploadFontFile } from '@/lib/storage';

export interface CustomFont {
  id: string;
  name: string;
  family: string;
  url: string;
  format: string;
  originalFileName: string;
  uploadDate: Date;
  storagePath?: string; // Path in Supabase storage
}

export interface FontValidationResult {
  isValid: boolean;
  error?: string;
  format?: string;
  size?: number;
}

/**
 * Supported font formats with their MIME types and extensions
 */
export const SUPPORTED_FONT_FORMATS = {
  'woff2': {
    mimeTypes: ['font/woff2', 'application/font-woff2'],
    extensions: ['.woff2'],
    priority: 1,
    description: 'WOFF2 (Recommended)'
  },
  'woff': {
    mimeTypes: ['font/woff', 'application/font-woff'],
    extensions: ['.woff'],
    priority: 2,
    description: 'WOFF'
  },
  'truetype': {
    mimeTypes: ['font/ttf', 'application/x-font-ttf', 'font/truetype'],
    extensions: ['.ttf'],
    priority: 3,
    description: 'TrueType'
  },
  'opentype': {
    mimeTypes: ['font/otf', 'application/x-font-opentype', 'font/opentype'],
    extensions: ['.otf'],
    priority: 4,
    description: 'OpenType'
  }
} as const;

/**
 * Maximum file size for font uploads (2MB)
 */
export const MAX_FONT_SIZE = 2 * 1024 * 1024;

/**
 * Validates a font file
 */
export function validateFontFile(file: File): FontValidationResult {
  // Check file size
  if (file.size > MAX_FONT_SIZE) {
    return {
      isValid: false,
      error: `Font file is too large. Maximum size is ${Math.round(MAX_FONT_SIZE / 1024 / 1024)}MB.`
    };
  }

  // Check file extension
  const extension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
  let detectedFormat: string | null = null;

  for (const [format, config] of Object.entries(SUPPORTED_FONT_FORMATS)) {
    if (config.extensions.includes(extension)) {
      detectedFormat = format;
      break;
    }
  }

  if (!detectedFormat) {
    const supportedExts = Object.values(SUPPORTED_FONT_FORMATS)
      .flatMap(config => config.extensions)
      .join(', ');
    return {
      isValid: false,
      error: `Unsupported font format. Supported formats: ${supportedExts}`
    };
  }

  // Additional MIME type validation if available
  if (file.type) {
    const formatConfig = SUPPORTED_FONT_FORMATS[detectedFormat as keyof typeof SUPPORTED_FONT_FORMATS];
    if (!formatConfig.mimeTypes.includes(file.type)) {
      console.warn(`MIME type mismatch: expected ${formatConfig.mimeTypes.join(' or ')}, got ${file.type}`);
    }
  }

  return {
    isValid: true,
    format: detectedFormat,
    size: file.size
  };
}

/**
 * Generates a unique font family name
 */
export function generateFontFamilyName(originalName: string): string {
  // Remove extension and clean up the name
  const baseName = originalName.replace(/\.[^/.]+$/, '');
  const cleanName = baseName
    .replace(/[^a-zA-Z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .toLowerCase();
  
  // Add timestamp to ensure uniqueness
  const timestamp = Date.now().toString(36);
  return `custom-${cleanName}-${timestamp}`;
}

/**
 * Creates a CSS @font-face rule
 */
export function createFontFaceCSS(font: CustomFont): string {
  return `
@font-face {
  font-family: "${font.family}";
  src: url("${font.url}") format("${font.format}");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}`;
}

/**
 * Injects CSS into the document
 */
export function injectFontCSS(css: string, fontId: string): void {
  // Remove existing style element for this font if it exists
  const existingStyle = document.getElementById(`custom-font-${fontId}`);
  if (existingStyle) {
    existingStyle.remove();
  }

  // Create and inject new style element
  const styleElement = document.createElement('style');
  styleElement.id = `custom-font-${fontId}`;
  styleElement.textContent = css;
  document.head.appendChild(styleElement);
}

/**
 * Removes font CSS from the document
 */
export function removeFontCSS(fontId: string): void {
  const styleElement = document.getElementById(`custom-font-${fontId}`);
  if (styleElement) {
    styleElement.remove();
  }
}

/**
 * Loads a custom font file and returns font data (legacy blob URL method)
 */
export async function loadCustomFont(file: File): Promise<CustomFont> {
  const validation = validateFontFile(file);

  if (!validation.isValid) {
    throw new Error(validation.error);
  }

  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (event) => {
      try {
        const arrayBuffer = event.target?.result as ArrayBuffer;
        const blob = new Blob([arrayBuffer], {
          type: file.type || `font/${validation.format}`
        });
        const url = URL.createObjectURL(blob);

        const fontId = crypto.randomUUID();
        const fontFamily = generateFontFamilyName(file.name);

        const customFont: CustomFont = {
          id: fontId,
          name: file.name.replace(/\.[^/.]+$/, ''), // Remove extension for display
          family: fontFamily,
          url: url,
          format: validation.format!,
          originalFileName: file.name,
          uploadDate: new Date()
        };

        resolve(customFont);
      } catch (error) {
        reject(new Error(`Failed to process font file: ${error}`));
      }
    };

    reader.onerror = () => {
      reject(new Error('Failed to read font file'));
    };

    reader.readAsArrayBuffer(file);
  });
}

/**
 * Uploads a custom font file to Supabase storage and returns font data
 */
export async function uploadCustomFont(file: File, userId: string): Promise<CustomFont> {
  const validation = validateFontFile(file);

  if (!validation.isValid) {
    throw new Error(validation.error);
  }

  // Upload to Supabase storage
  const uploadResult = await uploadFontFile(file, userId);

  if (!uploadResult.success) {
    throw new Error(uploadResult.error || 'Font upload failed');
  }

  const fontId = crypto.randomUUID();
  const fontFamily = generateFontFamilyName(file.name);

  const customFont: CustomFont = {
    id: fontId,
    name: file.name.replace(/\.[^/.]+$/, ''), // Remove extension for display
    family: fontFamily,
    url: uploadResult.url!,
    format: validation.format!,
    originalFileName: file.name,
    uploadDate: new Date(),
    storagePath: uploadResult.path
  };

  return customFont;
}

/**
 * Cleans up font resources
 */
export function cleanupFont(font: CustomFont): void {
  // Remove CSS
  removeFontCSS(font.id);
  
  // Revoke object URL to free memory
  if (font.url.startsWith('blob:')) {
    URL.revokeObjectURL(font.url);
  }
}

/**
 * Gets font format description for UI display
 */
export function getFontFormatDescription(format: string): string {
  const config = SUPPORTED_FONT_FORMATS[format as keyof typeof SUPPORTED_FONT_FORMATS];
  return config?.description || format.toUpperCase();
}

/**
 * Formats file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
