import { supabase } from './supabase'
import { CustomFont } from './storage'

export interface TypographyConfig {
  fontSize: number
  lineHeight: number
  letterSpacing: number
  wordSpacing: number
  textColor: string
  marginTop: number
  marginBottom: number
  marginLeft: number
  marginRight: number
  handwritingEffects: {
    shadowEnabled: boolean
    shadowIntensity: number
    shadowBlur: number
    shadowOffsetX: number
    shadowOffsetY: number
    jitterEnabled: boolean
    jitterIntensity: number
    pressureVariation: boolean
    pressureIntensity: number
  }
}

export interface EditorState {
  text: string
  selectedFont: string
  background: string
  customBackground: string
  typographyConfig: TypographyConfig
  textPosition: { x: number; y: number }
  showPageNumbers: boolean
  customFonts: CustomFont[]
}

export interface EditorStateResult {
  success: boolean
  data?: EditorState
  error?: string
}

// Default editor state
export const defaultEditorState: EditorState = {
  text: '',
  selectedFont: 'handwriting',
  background: 'lined',
  customBackground: '',
  typographyConfig: {
    fontSize: 18,
    lineHeight: 1.6,
    letterSpacing: 0,
    wordSpacing: 0,
    textColor: '#000000',
    marginTop: 10,
    marginBottom: 10,
    marginLeft: 10,
    marginRight: 10,
    handwritingEffects: {
      shadowEnabled: false,
      shadowIntensity: 30,
      shadowBlur: 2,
      shadowOffsetX: 1,
      shadowOffsetY: 1,
      jitterEnabled: false,
      jitterIntensity: 20,
      pressureVariation: false,
      pressureIntensity: 15
    }
  },
  textPosition: { x: 0, y: 0 },
  showPageNumbers: false,
  customFonts: []
}

// localStorage keys for fallback
const STORAGE_KEYS = {
  text: 'handwriting-editor-text',
  font: 'handwriting-editor-font',
  background: 'handwriting-editor-background',
  typography: 'handwriting-editor-typography',
  pageNumbers: 'handwriting-editor-show-page-numbers',
  customBg: 'handwriting-editor-custom-background',
  textPosition: 'handwriting-editor-text-position',
  customFonts: 'handwriting-editor-custom-fonts'
}

/**
 * Load editor state from Supabase with localStorage fallback
 */
export async function loadEditorState(userId: string): Promise<EditorStateResult> {
  try {
    // Try to load from Supabase first
    const { data, error } = await supabase
      .from('user_editor_state')
      .select('editor_state')
      .eq('user_id', userId)
      .single()

    if (!error && data?.editor_state) {
      return {
        success: true,
        data: { ...defaultEditorState, ...data.editor_state }
      }
    }

    // Fallback to localStorage if Supabase fails or no data
    console.log('Loading from localStorage fallback')
    const localState = loadFromLocalStorage()
    
    // If we have local data, save it to Supabase for future use
    if (localState && Object.keys(localState).length > 0) {
      await saveEditorState(userId, localState)
    }

    return {
      success: true,
      data: { ...defaultEditorState, ...localState }
    }
  } catch (error) {
    console.error('Error loading editor state:', error)
    
    // Final fallback to localStorage
    const localState = loadFromLocalStorage()
    return {
      success: false,
      data: { ...defaultEditorState, ...localState },
      error: 'Failed to load from database, using local storage'
    }
  }
}

/**
 * Save editor state to Supabase with localStorage backup
 */
export async function saveEditorState(userId: string, state: Partial<EditorState>): Promise<boolean> {
  try {
    // Save to localStorage as backup
    saveToLocalStorage(state)

    // Save to Supabase
    const { error } = await supabase
      .from('user_editor_state')
      .upsert({
        user_id: userId,
        editor_state: state
      }, {
        onConflict: 'user_id'
      })

    if (error) {
      console.error('Error saving to Supabase:', error)
      return false
    }

    return true
  } catch (error) {
    console.error('Error saving editor state:', error)
    return false
  }
}

/**
 * Load state from localStorage (fallback)
 */
function loadFromLocalStorage(): Partial<EditorState> {
  try {
    const state: Partial<EditorState> = {}

    const savedText = localStorage.getItem(STORAGE_KEYS.text)
    const savedFont = localStorage.getItem(STORAGE_KEYS.font)
    const savedBackground = localStorage.getItem(STORAGE_KEYS.background)
    const savedTypography = localStorage.getItem(STORAGE_KEYS.typography)
    const savedPageNumbers = localStorage.getItem(STORAGE_KEYS.pageNumbers)
    const savedCustomBg = localStorage.getItem(STORAGE_KEYS.customBg)
    const savedTextPosition = localStorage.getItem(STORAGE_KEYS.textPosition)
    const savedCustomFonts = localStorage.getItem(STORAGE_KEYS.customFonts)

    if (savedText) state.text = savedText
    if (savedFont) state.selectedFont = savedFont
    if (savedBackground) state.background = savedBackground
    if (savedCustomBg) state.customBackground = savedCustomBg
    if (savedPageNumbers) state.showPageNumbers = savedPageNumbers === 'true'

    if (savedTypography) {
      try {
        state.typographyConfig = JSON.parse(savedTypography)
      } catch (e) {
        console.warn('Failed to parse typography config:', e)
      }
    }

    if (savedTextPosition) {
      try {
        state.textPosition = JSON.parse(savedTextPosition)
      } catch (e) {
        console.warn('Failed to parse text position:', e)
      }
    }

    if (savedCustomFonts) {
      try {
        state.customFonts = JSON.parse(savedCustomFonts)
      } catch (e) {
        console.warn('Failed to parse custom fonts:', e)
      }
    }

    return state
  } catch (error) {
    console.error('Error loading from localStorage:', error)
    return {}
  }
}

/**
 * Save state to localStorage (backup)
 */
function saveToLocalStorage(state: Partial<EditorState>): void {
  try {
    if (state.text !== undefined) {
      localStorage.setItem(STORAGE_KEYS.text, state.text)
    }
    if (state.selectedFont !== undefined) {
      localStorage.setItem(STORAGE_KEYS.font, state.selectedFont)
    }
    if (state.background !== undefined) {
      localStorage.setItem(STORAGE_KEYS.background, state.background)
    }
    if (state.customBackground !== undefined) {
      localStorage.setItem(STORAGE_KEYS.customBg, state.customBackground)
    }
    if (state.showPageNumbers !== undefined) {
      localStorage.setItem(STORAGE_KEYS.pageNumbers, state.showPageNumbers.toString())
    }
    if (state.typographyConfig !== undefined) {
      localStorage.setItem(STORAGE_KEYS.typography, JSON.stringify(state.typographyConfig))
    }
    if (state.textPosition !== undefined) {
      localStorage.setItem(STORAGE_KEYS.textPosition, JSON.stringify(state.textPosition))
    }
    if (state.customFonts !== undefined) {
      localStorage.setItem(STORAGE_KEYS.customFonts, JSON.stringify(state.customFonts))
    }
  } catch (error) {
    console.error('Error saving to localStorage:', error)
  }
}

/**
 * Clear all editor state (both Supabase and localStorage)
 */
export async function clearEditorState(userId: string): Promise<boolean> {
  try {
    // Clear from Supabase
    const { error } = await supabase
      .from('user_editor_state')
      .delete()
      .eq('user_id', userId)

    // Clear from localStorage
    Object.values(STORAGE_KEYS).forEach(key => {
      localStorage.removeItem(key)
    })

    return !error
  } catch (error) {
    console.error('Error clearing editor state:', error)
    return false
  }
}

/**
 * Debounced save function for real-time updates
 */
export function createDebouncedSave(userId: string, delay: number = 1000) {
  let timeoutId: NodeJS.Timeout | null = null

  return (state: Partial<EditorState>) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    timeoutId = setTimeout(() => {
      saveEditorState(userId, state)
    }, delay)
  }
}
