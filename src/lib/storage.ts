import { supabase } from './supabase'

export interface UploadResult {
  success: boolean
  url?: string
  error?: string
  path?: string
}

export interface CustomFont {
  id: string
  name: string
  family: string
  url: string
  format: string
  originalFileName: string
  uploadDate: Date
}

/**
 * Upload a background image to Supabase storage
 */
export async function uploadBackgroundImage(file: File, userId: string): Promise<UploadResult> {
  try {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
    if (!allowedTypes.includes(file.type)) {
      return {
        success: false,
        error: 'Invalid file type. Please upload a JPEG, PNG, WebP, or GIF image.'
      }
    }

    // Validate file size (5MB limit)
    const maxSize = 5 * 1024 * 1024 // 5MB
    if (file.size > maxSize) {
      return {
        success: false,
        error: 'File too large. Maximum size is 5MB.'
      }
    }

    // Generate unique filename
    const fileExt = file.name.split('.').pop()
    const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`
    const filePath = `${userId}/${fileName}`

    // Upload to Supabase storage
    const { data, error } = await supabase.storage
      .from('user-backgrounds')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      })

    if (error) {
      console.error('Storage upload error:', error)
      return {
        success: false,
        error: `Upload failed: ${error.message}`
      }
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from('user-backgrounds')
      .getPublicUrl(data.path)

    return {
      success: true,
      url: urlData.publicUrl,
      path: data.path
    }
  } catch (error) {
    console.error('Background upload error:', error)
    return {
      success: false,
      error: 'An unexpected error occurred during upload.'
    }
  }
}

/**
 * Upload a font file to Supabase storage
 */
export async function uploadFontFile(file: File, userId: string): Promise<UploadResult> {
  try {
    // Validate file type
    const allowedTypes = [
      'font/woff2', 'font/woff', 'font/ttf', 'font/otf',
      'application/font-woff2', 'application/font-woff',
      'application/x-font-ttf', 'application/x-font-opentype'
    ]
    
    if (!allowedTypes.includes(file.type)) {
      return {
        success: false,
        error: 'Invalid font file type. Please upload WOFF2, WOFF, TTF, or OTF files.'
      }
    }

    // Validate file size (2MB limit)
    const maxSize = 2 * 1024 * 1024 // 2MB
    if (file.size > maxSize) {
      return {
        success: false,
        error: 'Font file too large. Maximum size is 2MB.'
      }
    }

    // Generate unique filename
    const fileExt = file.name.split('.').pop()
    const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`
    const filePath = `${userId}/${fileName}`

    // Upload to Supabase storage
    const { data, error } = await supabase.storage
      .from('user-fonts')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      })

    if (error) {
      console.error('Font storage upload error:', error)
      return {
        success: false,
        error: `Font upload failed: ${error.message}`
      }
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from('user-fonts')
      .getPublicUrl(data.path)

    return {
      success: true,
      url: urlData.publicUrl,
      path: data.path
    }
  } catch (error) {
    console.error('Font upload error:', error)
    return {
      success: false,
      error: 'An unexpected error occurred during font upload.'
    }
  }
}

/**
 * Delete a background image from storage
 */
export async function deleteBackgroundImage(path: string): Promise<boolean> {
  try {
    const { error } = await supabase.storage
      .from('user-backgrounds')
      .remove([path])

    if (error) {
      console.error('Background delete error:', error)
      return false
    }

    return true
  } catch (error) {
    console.error('Background delete error:', error)
    return false
  }
}

/**
 * Delete a font file from storage
 */
export async function deleteFontFile(path: string): Promise<boolean> {
  try {
    const { error } = await supabase.storage
      .from('user-fonts')
      .remove([path])

    if (error) {
      console.error('Font delete error:', error)
      return false
    }

    return true
  } catch (error) {
    console.error('Font delete error:', error)
    return false
  }
}

/**
 * List user's background images
 */
export async function listUserBackgrounds(userId: string) {
  try {
    const { data, error } = await supabase.storage
      .from('user-backgrounds')
      .list(userId)

    if (error) {
      console.error('List backgrounds error:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('List backgrounds error:', error)
    return []
  }
}

/**
 * List user's font files
 */
export async function listUserFonts(userId: string) {
  try {
    const { data, error } = await supabase.storage
      .from('user-fonts')
      .list(userId)

    if (error) {
      console.error('List fonts error:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('List fonts error:', error)
    return []
  }
}
