/**
 * Test file to verify Supabase integration
 * This file can be used to test the storage and database functionality
 */

import { supabase } from './lib/supabase';
import { uploadBackgroundImage, uploadFontFile } from './lib/storage';
import { loadEditorState, saveEditorState } from './lib/editorState';

// Test user ID (replace with actual user ID when testing)
const TEST_USER_ID = 'test-user-123';

/**
 * Test editor state save/load functionality
 */
export async function testEditorState() {
  console.log('Testing editor state functionality...');
  
  const testState = {
    text: 'Hello, Supabase!',
    selectedFont: 'handwriting',
    background: 'lined',
    customBackground: '',
    typographyConfig: {
      fontSize: 18,
      lineHeight: 1.6,
      letterSpacing: 0,
      wordSpacing: 0,
      textColor: '#000000',
      marginTop: 10,
      marginBottom: 10,
      marginLeft: 10,
      marginRight: 10,
      handwritingEffects: {
        shadowEnabled: false,
        shadowIntensity: 30,
        shadowBlur: 2,
        shadowOffsetX: 1,
        shadowOffsetY: 1,
        jitterEnabled: false,
        jitterIntensity: 20,
        pressureVariation: false,
        pressureIntensity: 15
      }
    },
    textPosition: { x: 0, y: 0 },
    showPageNumbers: false,
    customFonts: []
  };

  try {
    // Test save
    console.log('Saving test state...');
    const saveResult = await saveEditorState(TEST_USER_ID, testState);
    console.log('Save result:', saveResult);

    // Test load
    console.log('Loading test state...');
    const loadResult = await loadEditorState(TEST_USER_ID);
    console.log('Load result:', loadResult);

    if (loadResult.success && loadResult.data) {
      console.log('✅ Editor state test passed!');
      console.log('Loaded text:', loadResult.data.text);
    } else {
      console.log('❌ Editor state test failed:', loadResult.error);
    }
  } catch (error) {
    console.error('❌ Editor state test error:', error);
  }
}

/**
 * Test storage bucket access
 */
export async function testStorageBuckets() {
  console.log('Testing storage bucket access...');
  
  try {
    // Test listing user-backgrounds bucket
    const { data: bgData, error: bgError } = await supabase.storage
      .from('user-backgrounds')
      .list('test-folder', { limit: 1 });
    
    if (bgError) {
      console.log('Background bucket error (expected for empty folder):', bgError.message);
    } else {
      console.log('✅ Background bucket accessible:', bgData);
    }

    // Test listing user-fonts bucket
    const { data: fontData, error: fontError } = await supabase.storage
      .from('user-fonts')
      .list('test-folder', { limit: 1 });
    
    if (fontError) {
      console.log('Font bucket error (expected for empty folder):', fontError.message);
    } else {
      console.log('✅ Font bucket accessible:', fontData);
    }

    console.log('✅ Storage bucket test completed!');
  } catch (error) {
    console.error('❌ Storage bucket test error:', error);
  }
}

/**
 * Test database table access
 */
export async function testDatabaseAccess() {
  console.log('Testing database table access...');
  
  try {
    // Test reading from user_editor_state table
    const { data, error } = await supabase
      .from('user_editor_state')
      .select('*')
      .limit(1);
    
    if (error) {
      console.log('Database error:', error.message);
    } else {
      console.log('✅ Database table accessible:', data);
    }
  } catch (error) {
    console.error('❌ Database test error:', error);
  }
}

/**
 * Run all tests
 */
export async function runAllTests() {
  console.log('🧪 Starting Supabase integration tests...\n');
  
  await testDatabaseAccess();
  console.log('');
  
  await testStorageBuckets();
  console.log('');
  
  await testEditorState();
  console.log('');
  
  console.log('🏁 All tests completed!');
}

// Export for manual testing in browser console
if (typeof window !== 'undefined') {
  (window as any).testSupabase = {
    runAllTests,
    testEditorState,
    testStorageBuckets,
    testDatabaseAccess
  };
}
