import { FC, useRef, useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Upload, Check, X, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { uploadBackgroundImage } from '@/lib/storage';
import { useAuth } from '@/contexts/AuthContext';

interface BackgroundOption {
  value: string;
  label: string;
  preview: string;
  description?: string;
}

interface BackgroundGalleryProps {
  selectedBackground: string;
  onBackgroundChange: (background: string) => void;
  customBackground: string;
  onCustomBackgroundChange: (url: string) => void;
  className?: string;
}

const backgroundOptions: BackgroundOption[] = [
  {
    value: 'lined',
    label: 'Lined Paper',
    preview: 'linear-gradient(to bottom, transparent 0px, transparent 24px, #e5e7eb 24px, #e5e7eb 25px, transparent 25px)',
    description: 'Classic notebook lines'
  },
  {
    value: 'plain',
    label: 'Plain White',
    preview: '#ffffff',
    description: 'Clean white background'
  },
  {
    value: 'warm-white',
    label: 'Warm White',
    preview: 'linear-gradient(135deg, #fefcf3 0%, #fdf8e8 50%, #fcf5e0 100%)',
    description: 'Yellow light ambiance'
  },
  {
    value: 'cool-white',
    label: 'Cool White',
    preview: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%)',
    description: 'Blue light ambiance'
  },
  {
    value: 'fluorescent',
    label: 'Fluorescent',
    preview: 'linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 50%, #d1fae5 100%)',
    description: 'Office lighting'
  },
  {
    value: 'aged-paper',
    label: 'Aged Paper',
    preview: 'linear-gradient(135deg, #faf7f0 0%, #f5f0e8 50%, #f0ebe3 100%)',
    description: 'Vintage appearance'
  },
  {
    value: 'textured-white',
    label: 'Textured',
    preview: 'linear-gradient(45deg, #ffffff 25%, #fefefe 25%, #fefefe 50%, #ffffff 50%, #ffffff 75%, #fefefe 75%, #fefefe)',
    description: 'Subtle texture'
  },
  {
    value: 'dots',
    label: 'Dotted',
    preview: 'radial-gradient(circle, #d1d5db 1px, transparent 1px)',
    description: 'Dot grid pattern'
  },
  {
    value: 'graph',
    label: 'Graph Paper',
    preview: 'linear-gradient(to right, #e5e7eb 1px, transparent 1px), linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)',
    description: 'Grid lines'
  },
  {
    value: 'music',
    label: 'Music Staff',
    preview: 'repeating-linear-gradient(0deg, transparent, transparent 30px, #000000 30px, #000000 31px, transparent 31px, transparent 120px)',
    description: 'Musical notation'
  },
  {
    value: 'parchment',
    label: 'Parchment',
    preview: 'linear-gradient(45deg, #f4f1e8 25%, #f7f4ec 25%, #f7f4ec 50%, #f4f1e8 50%, #f4f1e8 75%, #f7f4ec 75%, #f7f4ec)',
    description: 'Ancient scroll look'
  },
  {
    value: 'blueprint',
    label: 'Blueprint',
    preview: '#1e3a8a',
    description: 'Technical drawing style'
  }
];

export const BackgroundGallery: FC<BackgroundGalleryProps> = ({
  selectedBackground,
  onBackgroundChange,
  customBackground,
  onCustomBackgroundChange,
  className
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  const handleCustomUpload = () => {
    fileInputRef.current?.click();
  };

  const validateFile = (file: File): string | null => {
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
      return 'Please select a valid image file (JPEG, PNG, WebP, or GIF)';
    }

    // Check file size (max 5MB to match Supabase storage limit)
    if (file.size > 5 * 1024 * 1024) {
      return 'Image file size must be less than 5MB';
    }

    return null;
  };

  const { user } = useAuth();

  const processFile = useCallback(async (file: File) => {
    const error = validateFile(file);
    if (error) {
      setUploadError(error);
      return;
    }

    if (!user) {
      setUploadError('You must be logged in to upload custom backgrounds');
      return;
    }

    setIsUploading(true);
    setUploadError(null);

    try {
      const result = await uploadBackgroundImage(file, user.id);

      if (result.success && result.url) {
        onCustomBackgroundChange(result.url);
        onBackgroundChange('custom');
      } else {
        setUploadError(result.error || 'Upload failed');
      }
    } catch (error) {
      console.error('Upload error:', error);
      setUploadError('An unexpected error occurred during upload');
    } finally {
      setIsUploading(false);
    }
  }, [user, onCustomBackgroundChange, onBackgroundChange]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      processFile(files[0]);
    }
  }, [processFile]);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      processFile(file);
    }
  }, [processFile]);

  return (
    <div className={cn("space-y-4", className)}>
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3">
        {backgroundOptions.map((option) => (
          <Card
            key={option.value}
            className={cn(
              "relative cursor-pointer transition-all duration-200 hover:scale-105 hover:shadow-md group",
              selectedBackground === option.value
                ? "ring-2 ring-indigo-500 shadow-lg"
                : "hover:ring-1 hover:ring-neutral-300"
            )}
            onClick={() => onBackgroundChange(option.value)}
          >
            <div className="aspect-[3/4] p-2">
              {/* Preview */}
              <div
                className="w-full h-full rounded border border-neutral-200 relative overflow-hidden"
                style={{
                  background: option.preview,
                  backgroundSize: option.value === 'dots' ? '20px 20px' : 
                                option.value === 'graph' ? '20px 20px' :
                                option.value === 'textured-white' ? '8px 8px' :
                                option.value === 'parchment' ? '20px 20px' : 'cover'
                }}
              >
                {/* Sample text overlay */}
                <div className="absolute inset-2 flex items-center justify-center">
                  <div 
                    className={cn(
                      "text-xs font-handwriting leading-tight text-center",
                      option.value === 'blueprint' ? "text-white" : "text-neutral-700"
                    )}
                  >
                    Aa
                  </div>
                </div>
                
                {/* Selection indicator */}
                {selectedBackground === option.value && (
                  <div className="absolute top-1 right-1 bg-indigo-500 text-white rounded-full p-1">
                    <Check className="h-3 w-3" />
                  </div>
                )}
              </div>
              
              {/* Label */}
              <div className="mt-2 text-center">
                <div className="text-xs font-medium text-neutral-700 dark:text-neutral-300 truncate">
                  {option.label}
                </div>
                <div className="text-xs text-neutral-500 dark:text-neutral-400 truncate">
                  {option.description}
                </div>
              </div>
            </div>
          </Card>
        ))}

        {/* Custom Background Option */}
        <Card
          className={cn(
            "relative cursor-pointer transition-all duration-200 hover:scale-105 hover:shadow-md group",
            selectedBackground === 'custom'
              ? "ring-2 ring-indigo-500 shadow-lg"
              : "hover:ring-1 hover:ring-neutral-300",
            isDragOver && "ring-2 ring-blue-400 bg-blue-50 dark:bg-blue-900/20"
          )}
          onClick={handleCustomUpload}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className="aspect-[3/4] p-2">
            {/* Preview */}
            <div
              className={cn(
                "w-full h-full rounded border border-neutral-200 relative overflow-hidden flex items-center justify-center transition-all",
                isDragOver && "border-blue-400 border-dashed",
                isUploading && "opacity-50"
              )}
              style={{
                backgroundImage: customBackground ? `url(${customBackground})` : 'none',
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                backgroundColor: customBackground ? 'transparent' : '#f9fafb'
              }}
            >
              {isUploading ? (
                <div className="flex flex-col items-center gap-1">
                  <div className="w-4 h-4 border-2 border-indigo-500 border-t-transparent rounded-full animate-spin" />
                  <span className="text-xs text-neutral-500">Uploading...</span>
                </div>
              ) : !customBackground ? (
                <div className="flex flex-col items-center gap-1">
                  <Upload className="h-6 w-6 text-neutral-400" />
                  {isDragOver && (
                    <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                      Drop image here
                    </span>
                  )}
                </div>
              ) : (
                <div className="absolute inset-2 flex items-center justify-center">
                  <div className="text-xs font-handwriting leading-tight text-center text-neutral-700">
                    Aa
                  </div>
                </div>
              )}

              {/* Selection indicator */}
              {selectedBackground === 'custom' && !isUploading && (
                <div className="absolute top-1 right-1 bg-indigo-500 text-white rounded-full p-1">
                  <Check className="h-3 w-3" />
                </div>
              )}
            </div>

            {/* Label */}
            <div className="mt-2 text-center">
              <div className="text-xs font-medium text-neutral-700 dark:text-neutral-300 truncate">
                Custom
              </div>
              <div className="text-xs text-neutral-500 dark:text-neutral-400 truncate">
                {isDragOver ? 'Drop to upload' : 'Click or drag image'}
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileInputChange}
        className="hidden"
      />

      {/* Error message */}
      {uploadError && (
        <div className="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <AlertCircle className="h-4 w-4 text-red-500" />
          <span className="text-sm text-red-700 dark:text-red-400">{uploadError}</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setUploadError(null)}
            className="ml-auto h-6 w-6 p-0 text-red-500 hover:text-red-700"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      )}

      {/* Custom background info */}
      {selectedBackground === 'custom' && customBackground && !uploadError && (
        <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded border overflow-hidden shadow-sm">
              <img
                src={customBackground}
                alt="Custom background"
                className="w-full h-full object-cover"
              />
            </div>
            <div>
              <span className="text-sm font-medium text-green-700 dark:text-green-400">
                Custom background applied
              </span>
              <p className="text-xs text-green-600 dark:text-green-500">
                Your image is now being used as the paper background
              </p>
            </div>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCustomUpload}
              className="text-xs border-green-300 text-green-700 hover:bg-green-100 dark:border-green-700 dark:text-green-400 dark:hover:bg-green-900/30"
            >
              Change
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                onBackgroundChange('plain');
                setUploadError(null);
              }}
              className="text-xs text-green-600 hover:text-green-800 dark:text-green-500 dark:hover:text-green-300"
            >
              Remove
            </Button>
          </div>
        </div>
      )}

      {/* Upload instructions */}
      {!customBackground && !uploadError && (
        <div className="text-center p-4 bg-neutral-50 dark:bg-neutral-800/50 rounded-lg border border-dashed border-neutral-300 dark:border-neutral-600">
          <Upload className="h-8 w-8 text-neutral-400 mx-auto mb-2" />
          <p className="text-sm text-neutral-600 dark:text-neutral-400 mb-1">
            Upload a custom background image
          </p>
          <p className="text-xs text-neutral-500 dark:text-neutral-500">
            Drag and drop an image file or click to browse • Max 5MB • JPEG, PNG, WebP, GIF supported
          </p>
        </div>
      )}
    </div>
  );
};
