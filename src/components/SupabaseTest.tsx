import React, { useState } from 'react'
import { supabase } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export const SupabaseTest: React.FC = () => {
  const [testResult, setTestResult] = useState<string>('')
  const [testing, setTesting] = useState(false)

  const testConnection = async () => {
    setTesting(true)
    setTestResult('Testing connection...')
    
    try {
      // Test basic connection
      const { data, error } = await supabase
        .from('profiles')
        .select('count')
        .limit(1)
      
      if (error) {
        setTestResult(`❌ Connection failed: ${error.message}`)
      } else {
        setTestResult('✅ Supabase connection working!')
      }
    } catch (error) {
      setTestResult(`❌ Network error: ${error}`)
    } finally {
      setTesting(false)
    }
  }

  const testAuth = async () => {
    setTesting(true)
    setTestResult('Testing auth...')
    
    try {
      const { data: { session }, error } = await supabase.auth.getSession()
      
      if (error) {
        setTestResult(`❌ Auth error: ${error.message}`)
      } else if (session) {
        setTestResult(`✅ Auth working! User: ${session.user.email}`)
      } else {
        setTestResult('ℹ️ No active session (not logged in)')
      }
    } catch (error) {
      setTestResult(`❌ Auth test failed: ${error}`)
    } finally {
      setTesting(false)
    }
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Supabase Connection Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button 
            onClick={testConnection} 
            disabled={testing}
            size="sm"
          >
            Test DB
          </Button>
          <Button 
            onClick={testAuth} 
            disabled={testing}
            size="sm"
          >
            Test Auth
          </Button>
        </div>
        
        {testResult && (
          <div className="p-2 bg-gray-50 rounded text-sm">
            {testResult}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
